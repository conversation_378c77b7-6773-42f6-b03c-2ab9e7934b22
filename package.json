{"name": "rosen-chains", "private": true, "workspaces": {"packages": ["packages/abstract-chain", "packages/chains/*", "packages/networks/*"]}, "scripts": {"build": "npm run build --workspaces", "coverage": "npm run coverage --workspaces", "lint": "npm run lint --workspaces", "prepare": "husky install", "release": "npm run release --workspaces", "test": "npm run test --workspaces", "type-check": "npm run type-check --workspaces", "version": "npx changeset version && npm i"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@rosen-bridge/changeset-formatter": "^1.0.0", "husky": "^8.0.1", "lint-staged": "^13.0.3"}, "engines": {"node": ">=20.11.0"}}