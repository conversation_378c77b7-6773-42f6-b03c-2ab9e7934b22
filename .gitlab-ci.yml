image: node:20.11.0

stages:
  - installation
  - build
  - type_check
  - lint
  - test
  - changeset

installation:
  stage: installation
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: push
    paths:
      - node_modules
      - '**/node_modules'
  script:
    - npm ci

build:
  stage: build
  cache:
    - key: $CI_COMMIT_REF_NAME
      policy: pull-push
      paths:
        - node_modules
        - '**/node_modules'
        - '**/dist'
  script:
    - npm run build

type_check:
  stage: type_check
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - '**/node_modules'
      - '**/dist'
  script:
    - npm run type-check

lint:
  stage: lint
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - '**/node_modules'
      - '**/dist'
  script:
    - npm run lint

test:
  stage: test
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - '**/node_modules'
      - '**/dist'
  script:
    - npm run coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: '**/coverage/cobertura-coverage.xml'

changeset:
  stage: changeset
  cache:
    key: $CI_COMMIT_REF_NAME
    policy: pull
    paths:
      - node_modules
      - '**/node_modules'
      - '**/dist'
  before_script:
    - git fetch origin dev
  script:
    - npx changeset status --since=origin/dev
