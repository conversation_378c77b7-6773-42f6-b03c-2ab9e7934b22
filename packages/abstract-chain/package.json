{"name": "@rosen-chains/abstract-chain", "version": "14.0.0", "description": "this project contains abstract classes to implement any chain for Rosen-bridge", "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "files": ["dist", "CHANGELOG.md"], "type": "module", "scripts": {"prettify": "prettier --write . --ignore-path .gitignore", "lint": "eslint --fix . && npm run prettify", "build": "tsc --build tsconfig.build.json", "type-check": "tsc --noEmit", "test": "NODE_OPTIONS='--import tsx' vitest", "coverage": "npm run test -- --run --coverage", "release": "npm run test -- --run && npm run build && npm publish --access public"}, "repository": {"type": "git", "url": "git+https://github.com/rosen-bridge/rosen-chains.git"}, "license": "MIT", "dependencies": {"@rosen-bridge/abstract-box-selection": "^0.2.2", "@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-bridge/json-bigint": "^0.1.0", "@rosen-bridge/minimum-fee": "^2.2.3", "@rosen-bridge/rosen-extractor": "^8.0.0", "@rosen-bridge/tokens": "^3.1.1", "blakejs": "^1.2.1"}, "devDependencies": {"@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.26.0", "@vitest/coverage-istanbul": "^3.1.4", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.7.1", "tsx": "^4.19.4", "typescript": "^4.9.5", "vitest": "^3.1.4"}, "engines": {"node": ">=20.11.0"}, "directories": {"lib": "lib", "test": "tests"}, "keywords": ["rosen"]}