{"name": "@rosen-chains/ethereum", "version": "2.1.0", "description": "this project contains ethereum chain for Rosen-bridge", "repository": {"type": "git", "url": "git+https://github.com/rosen-bridge/rosen-chains.git"}, "license": "MIT", "author": "Rosen Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "CHANGELOG.md"], "scripts": {"build": "tsc --build tsconfig.build.json", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore", "release": "npm run build && npm publish --access public", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitest/coverage-istanbul": "^3.1.4", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "tsx": "^4.19.4", "typescript": "^5.3.3", "vitest": "^3.1.4"}, "engines": {"node": ">=20.11.0"}, "dependencies": {"@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-bridge/tokens": "^3.1.1", "@rosen-chains/abstract-chain": "^14.0.0", "@rosen-chains/evm": "^8.2.0"}}