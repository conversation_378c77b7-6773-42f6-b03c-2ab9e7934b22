import { RosenTokens } from '@rosen-bridge/tokens';
import { EventTrigger, PaymentOrder } from '@rosen-chains/abstract-chain';

export const testTokenMap: RosenTokens = [];

export const multiDecimalTokenMap: RosenTokens = [
  {
    ergo: {
      tokenId:
        '1c7435e608ab710c56bbe0f635e2a5e86ddf856f7d3d2d1d4dfefa62fbbfb9b4',
      name: 'testDOGE',
      decimals: 3,
      type: 'EIP-004',
      residency: 'wrapped',
      extra: {},
    },
    doge: {
      tokenId: 'doge',
      name: 'DO<PERSON>',
      decimals: 8,
      type: 'native',
      residency: 'native',
      extra: {},
    },
  },
];

export const transaction0PaymentTransaction = `{
    "network": "doge",
    "eventId": "",
    "txBytes": "70736274ff0100a002000000021d64f734069af52ed206f5b22157b4f21dffa6e5ce72c6f74db458638e3a62a20000000000ffffffff0e3b1137334f790084a12b62a598eac191f2ff7b25df9749e3d92d58cbbffdf70200000000ffffffff0200e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc853b9e8020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000100f901000000017af0d5f7b6fc3aa277e54a16f372f71e33cf9fb3632227397a1715008fb05836010000006b48304502210081c051556843abc8bfec58fd8bb6a9600f32944bf1d071998b16c42b399e7bb702200ab5c55de9a43967fe7c7f7726fabe2a34b097eb4223c4440d2dffab7e43d0c50121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff030065cd1d000000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000000000e6a0c48656c6c6f20526f73656e21480752bc030000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000100fd1f0102000000015de5e7ecd6c60bf37591d4c23a6747644040b71edbe209231542c848c7ef737f020000006a473044022069ef1e50e6cd355179f68cdf42e7f68ff442a005b4906f40aa846d945208eaf1022037ce2ecc0c294eb46f875bac50edffbe610247a2769c5dc2ad8e8f18f6155daf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff030000000000000000356a33000000000005f5e10000000000009896802103e5bedab3f782ef17a73e9bdc41ee0e18c3ab477400f35bcf7caa54171db7ff3600e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc82944d3020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000000",
    "txId": "e7bb0677b2a0542e3ecb8d3f29ea91ae3500b05201f1e97e6bef7e724c4aa476",
    "txType": "manual",
    "inputUtxos": [
      "{\\"txId\\":\\"a2623a8e6358b44df7c672cee5a6ff1df2b45721b2f506d22ef59a0634f7641d\\",\\"index\\":0,\\"value\\":500000000}",
      "{\\"txId\\":\\"f7fdbfcb582dd9e34997df257bfff291c1ea98a5622ba18400794f3337113b0e\\",\\"index\\":2,\\"value\\":12134394312}"
    ]
  }`;
export const transaction0Input0BoxId =
  'a2623a8e6358b44df7c672cee5a6ff1df2b45721b2f506d22ef59a0634f7641d.0';
export const transaction0Input1BoxId =
  'f7fdbfcb582dd9e34997df257bfff291c1ea98a5622ba18400794f3337113b0e.2';
export const transaction0InputIds = [
  transaction0Input0BoxId,
  transaction0Input1BoxId,
];

// Add additional transactions and data from Bitcoin testData
export const transaction1PaymentTransaction = `{
    "network": "doge",
    "eventId": "",
    "txBytes": "70736274ff01007702000000010b914262e601be6432014d82b519bf595eee40f226a673a72ca82760965ceb5a0100000000ffffffff0200e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc81861e0020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000100fd760102000000021d64f734069af52ed206f5b22157b4f21dffa6e5ce72c6f74db458638e3a62a2000000006b4830450221009a589b1cb889e0796cff6de767790866822bd45356425b7c4c4e4efcef4e6ea30220310bbc0a5fcfca1f84822e5eaaa665edcf817b1320e2229303d0caf877a2f1ac0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff0e3b1137334f790084a12b62a598eac191f2ff7b25df9749e3d92d58cbbffdf7020000006b483045022100b97a8f630779b1be32108edaa09e8c57bb0efd19b96f76cf1ab5eed592433f2502207f61878cb0bb3ef40c27cc38d5332b1ac5d2e34120cd244d107d5881f8c2fddf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff0200e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc853b9e8020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000000",
    "txId": "a5155dfe365c58006e28fb7d4c5d33dcfdd771f9a85612329da632083306e67e",
    "txType": "manual",
    "inputUtxos": [
      "{\\"txId\\":\\"5aeb5c966027a82ca773a626f240ee5e59bf19b5824d013264be01e66242910b\\",\\"index\\":1,\\"value\\":12494394312}"
    ]
  }`;

export const transaction1InputIds = [
  '5aeb5c966027a82ca773a626f240ee5e59bf19b5824d013264be01e66242910b.1',
];

export const transactionOpReturnPaymentTransaction = `{
  "network": "doge",
  "eventId": "",
  "txBytes": "70736274ff0100880200000001972da36330161ef9af99788ccc7261f81e2a046049d1ee65ad724288159633640100000000ffffffff0300000000000000000e6a0c6161616161616161616161618442993b00000000160014828037cbcbed02c6d9948e51b89c44da3a3b81fca086010000000000160014b20272a6591937ba7d687dc889f3637ed40efa6a000000000001011f00ca9a3b00000000160014fdfe06abec6a565eff3604db30fd30069b2f2a2800000000",
  "txId": "a451d0c24a8c871f52707cf2fcf0cb35f5b1ac6c734fb5cd172893d48d782e91",
  "txType": "manual",
  "inputUtxos": [
    "{\\"txId\\":\\"64339615884272ad65eed14960042a1ef86172cc8c7899aff91e163063a32d97\\",\\"index\\":1,\\"value\\":1000000000}"
  ]
}`;

export const transaction0Assets = {
  inputAssets: {
    nativeToken: 12634394312n,
    tokens: [],
  },
  outputAssets: {
    nativeToken: 12634394312n,
    tokens: [],
  },
};
export const transaction0WrappedAssets = {
  inputAssets: {
    nativeToken: 126344n,
    tokens: [],
  },
  outputAssets: {
    nativeToken: 126344n,
    tokens: [],
  },
};
export const transaction2Order: PaymentOrder = [
  {
    address: 'DDd8APtwJLJZJxwmoh3YmP5oeBe9tcdUp4',
    assets: {
      nativeToken: 100000000n,
      tokens: [],
    },
  },
];
export const transaction2WrappedOrder: PaymentOrder = [
  {
    address: 'DDd8APtwJLJZJxwmoh3YmP5oeBe9tcdUp4',
    assets: {
      nativeToken: 1000n,
      tokens: [],
    },
  },
];
export const transaction0HashMessage0 =
  'ac36493a67e171161c0b33a0414af459cbd49955453ef68cbf55737567457d39';
export const transaction0Signature0 =
  '9a589b1cb889e0796cff6de767790866822bd45356425b7c4c4e4efcef4e6ea3310bbc0a5fcfca1f84822e5eaaa665edcf817b1320e2229303d0caf877a2f1ac';
export const transaction0HashMessage1 =
  '0c51a346037390c214ddb9c4b28aa44d7911553066ffa6ce77d1ff1b0fff0dc0';
export const transaction0Signature1 =
  'b97a8f630779b1be32108edaa09e8c57bb0efd19b96f76cf1ab5eed592433f257f61878cb0bb3ef40c27cc38d5332b1ac5d2e34120cd244d107d5881f8c2fddf';
export const transaction0SignedTxBytesHex =
  '70736274ff0100a002000000021d64f734069af52ed206f5b22157b4f21dffa6e5ce72c6f74db458638e3a62a20000000000ffffffff0e3b1137334f790084a12b62a598eac191f2ff7b25df9749e3d92d58cbbffdf70200000000ffffffff0200e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc853b9e8020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000100f901000000017af0d5f7b6fc3aa277e54a16f372f71e33cf9fb3632227397a1715008fb05836010000006b48304502210081c051556843abc8bfec58fd8bb6a9600f32944bf1d071998b16c42b399e7bb702200ab5c55de9a43967fe7c7f7726fabe2a34b097eb4223c4440d2dffab7e43d0c50121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff030065cd1d000000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000000000e6a0c48656c6c6f20526f73656e21480752bc030000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac0000000001076b4830450221009a589b1cb889e0796cff6de767790866822bd45356425b7c4c4e4efcef4e6ea30220310bbc0a5fcfca1f84822e5eaaa665edcf817b1320e2229303d0caf877a2f1ac0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037000100fd1f0102000000015de5e7ecd6c60bf37591d4c23a6747644040b71edbe209231542c848c7ef737f020000006a473044022069ef1e50e6cd355179f68cdf42e7f68ff442a005b4906f40aa846d945208eaf1022037ce2ecc0c294eb46f875bac50edffbe610247a2769c5dc2ad8e8f18f6155daf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff030000000000000000356a33000000000005f5e10000000000009896802103e5bedab3f782ef17a73e9bdc41ee0e18c3ab477400f35bcf7caa54171db7ff3600e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc82944d3020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac0000000001076b483045022100b97a8f630779b1be32108edaa09e8c57bb0efd19b96f76cf1ab5eed592433f2502207f61878cb0bb3ef40c27cc38d5332b1ac5d2e34120cd244d107d5881f8c2fddf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037000000';
export const transaction2BoxMapping = [
  {
    inputId:
      'a2623a8e6358b44df7c672cee5a6ff1df2b45721b2f506d22ef59a0634f7641d.0',
    serializedOutput:
      '{"txId":"5aeb5c966027a82ca773a626f240ee5e59bf19b5824d013264be01e66242910b","index":1,"value":12494394312}',
    txHex:
      '02000000021d64f734069af52ed206f5b22157b4f21dffa6e5ce72c6f74db458638e3a62a2000000006b4830450221009a589b1cb889e0796cff6de767790866822bd45356425b7c4c4e4efcef4e6ea30220310bbc0a5fcfca1f84822e5eaaa665edcf817b1320e2229303d0caf877a2f1ac0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff0e3b1137334f790084a12b62a598eac191f2ff7b25df9749e3d92d58cbbffdf7020000006b483045022100b97a8f630779b1be32108edaa09e8c57bb0efd19b96f76cf1ab5eed592433f2502207f61878cb0bb3ef40c27cc38d5332b1ac5d2e34120cd244d107d5881f8c2fddf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff0200e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc853b9e8020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000',
  },
  {
    inputId:
      'f7fdbfcb582dd9e34997df257bfff291c1ea98a5622ba18400794f3337113b0e.2',
    serializedOutput:
      '{"txId":"5aeb5c966027a82ca773a626f240ee5e59bf19b5824d013264be01e66242910b","index":1,"value":12494394312}',
    txHex:
      '02000000021d64f734069af52ed206f5b22157b4f21dffa6e5ce72c6f74db458638e3a62a2000000006b4830450221009a589b1cb889e0796cff6de767790866822bd45356425b7c4c4e4efcef4e6ea30220310bbc0a5fcfca1f84822e5eaaa665edcf817b1320e2229303d0caf877a2f1ac0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff0e3b1137334f790084a12b62a598eac191f2ff7b25df9749e3d92d58cbbffdf7020000006b483045022100b97a8f630779b1be32108edaa09e8c57bb0efd19b96f76cf1ab5eed592433f2502207f61878cb0bb3ef40c27cc38d5332b1ac5d2e34120cd244d107d5881f8c2fddf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff0200e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc853b9e8020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000',
  },
];

export const lockAddress = 'DHTom1rFwsgAn5raKU1nok8E5MdQ4GBkAN';
export const lockAddressPublicKey =
  '022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037';
export const lockUtxo = {
  txId: 'f1ac0a7ce8a45aa53ac245ea2178592f708c9ef38bee0a5bd88c9f08d47ce493',
  index: 1,
  scriptPubKey:
    '0345307e1165c99d12557bea11f8c8cd0f6bc057fb51952e824bc7c760fda07335',
  value: 3000000000n,
};

export const validEvent: EventTrigger = {
  height: 300,
  fromChain: 'doge',
  toChain: 'ergo',
  fromAddress: 'fromAddress',
  toAddress: 'toAddress',
  amount: '1000000',
  bridgeFee: '1000',
  networkFee: '5000',
  sourceChainTokenId: 'sourceTokenId',
  targetChainTokenId: 'targetTokenId',
  sourceTxId:
    '6e3dbf41a8e3dbf41a8cd0fe059a54cef8bb140322503d0555a9851f056825bc',
  sourceChainHeight: 1000,
  sourceBlockId:
    '01a33c00accaa91ebe0c946bffe1ec294280a3a51a90f7f4b011f3f37c29c5ed',
  WIDsHash: 'bb2b2272816e1e9993fc535c0cf57c668f5cd39c67cfcd55b4422b1aa87cd0c3',
  WIDsCount: 2,
};

// Add additional events and data from Bitcoin testData
export const invalidEvent: EventTrigger = {
  height: 300,
  fromChain: 'doge',
  toChain: 'ergo',
  fromAddress: 'fromAddress',
  toAddress: 'toAddress',
  amount: '5500',
  bridgeFee: '1000',
  networkFee: '5000',
  sourceChainTokenId: 'sourceTokenId',
  targetChainTokenId: 'targetTokenId',
  sourceTxId:
    '6e3dbf41a8e3dbf41a8cd0fe059a54cef8bb140322503d0555a9851f056825bc',
  sourceChainHeight: 1000,
  sourceBlockId:
    '01a33c00accaa91ebe0c946bffe1ec294280a3a51a90f7f4b011f3f37c29c5ed',
  WIDsHash: 'bb2b2272816e1e9993fc535c0cf57c668f5cd39c67cfcd55b4422b1aa87cd0c3',
  WIDsCount: 2,
};

export const validEventWithHighFee: EventTrigger = {
  height: 300,
  fromChain: 'doge',
  toChain: 'ergo',
  fromAddress: 'fromAddress',
  toAddress: 'toAddress',
  amount: '1000000',
  bridgeFee: '1000',
  networkFee: '900000',
  sourceChainTokenId: 'sourceTokenId',
  targetChainTokenId: 'targetTokenId',
  sourceTxId:
    '6e3dbf41a8e3dbf41a8cd0fe059a54cef8bb140322503d0555a9851f056825bc',
  sourceChainHeight: 1000,
  sourceBlockId:
    '01a33c00accaa91ebe0c946bffe1ec294280a3a51a90f7f4b011f3f37c29c5ed',
  WIDsHash: 'bb2b2272816e1e9993fc535c0cf57c668f5cd39c67cfcd55b4422b1aa87cd0c3',
  WIDsCount: 2,
};

export const dogecoinTx1 = {
  id: '6a1b9e7a755afb5d82ecaa5f432d51bd23e452ee1031fc99066e92788a075a84',
  inputs: [
    {
      txId: 'eff4900465d1603d12c1dc8f231a07ce2196c04196aa26bb80147bb152137aaf',
      index: 0,
      scriptPubKey: '0014bf1916dc33dbdd65f60d8b1f65eb35e8120835fc',
    },
  ],
  outputs: [
    {
      scriptPubKey:
        '6a4c3300000000007554fc820000000000962f582103f999da8e6e42660e4464d17d29e63bc006734a6710a24eb489b466323d3a9339',
      value: 0n,
    },
    {
      scriptPubKey:
        '0345307e1165c99d12557bea11f8c8cd0f6bc057fb51952e824bc7c760fda07335',
      value: 3000000000n,
    },
  ],
};

export const lockAddressUtxos = [
  {
    txId: 'a2623a8e6358b44df7c672cee5a6ff1df2b45721b2f506d22ef59a0634f7641d',
    index: 0,
    value: 500000000n,
    scriptPubKey:
      '0345307e1165c99d12557bea11f8c8cd0f6bc057fb51952e824bc7c760fda07335',
    txHex:
      '01000000017af0d5f7b6fc3aa277e54a16f372f71e33cf9fb3632227397a1715008fb05836010000006b48304502210081c051556843abc8bfec58fd8bb6a9600f32944bf1d071998b16c42b399e7bb702200ab5c55de9a43967fe7c7f7726fabe2a34b097eb4223c4440d2dffab7e43d0c50121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff030065cd1d000000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000000000000e6a0c48656c6c6f20526f73656e21480752bc030000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000',
  },
  {
    txId: 'f7fdbfcb582dd9e34997df257bfff291c1ea98a5622ba18400794f3337113b0e',
    index: 2,
    value: 12134394312n,
    scriptPubKey:
      '0345307e1165c99d12557bea11f8c8cd0f6bc057fb51952e824bc7c760fda07335',
    txHex:
      '02000000015de5e7ecd6c60bf37591d4c23a6747644040b71edbe209231542c848c7ef737f020000006a473044022069ef1e50e6cd355179f68cdf42e7f68ff442a005b4906f40aa846d945208eaf1022037ce2ecc0c294eb46f875bac50edffbe610247a2769c5dc2ad8e8f18f6155daf0121022b9ed0a9139042921decc62603a4a07357b444da2e0bd6a96c27155117913037ffffffff030000000000000000356a33000000000005f5e10000000000009896802103e5bedab3f782ef17a73e9bdc41ee0e18c3ab477400f35bcf7caa54171db7ff3600e1f505000000001976a9145d0e02100e393220f90ffce4485f809de4ff777c88acc82944d3020000001976a914872b67c8270a9eaf5c2abf632af3dea989d2e37188ac00000000',
  },
];

export const transaction2PaymentTransaction = `{
  "network": "doge",
  "eventId": "",
  "txBytes": "70736274ff01009a0200000002193a28a12c8be889390e48b30cf9c65096f3f51bc04c2475557096d0cfca4f220100000000ffffffffd2e6232676e35e104927f22d4c90bc367c684209a4937664bad886227cd95c4b0100000000ffffffff028063ef2700000000160014828037cbcbed02c6d9948e51b89c44da3a3b81fcaff9e08a00000000160014fdfe06abec6a565eff3604db30fd30069b2f2a28000000000001011f00ca9a3b00000000160014fdfe06abec6a565eff3604db30fd30069b2f2a280001011f0094357700000000160014fdfe06abec6a565eff3604db30fd30069b2f2a28000000",
  "txId": "5bc486302164841b32bdfa03f510590109e3520d0a0aa6a15edfea0c8e33a080",
  "txType": "manual",
  "inputUtxos": [
    "{\\"txId\\":\\"224fcacfd096705575244cc01bf5f39650c6f90cb3480e3989e88b2ca1283a19\\",\\"index\\":1,\\"value\\":1000000000}",
    "{\\"txId\\":\\"4b5cd97c2286d8ba647693a40942687c36bc904c2df22749105ee3762623e6d2\\",\\"index\\":1,\\"value\\":2000000000}"
  ]
}`;

export const coveredBoxes = [
  {
    txId: '5aeb5c966027a82ca773a626f240ee5e59bf19b5824d013264be01e66242910b',
    index: 1,
    value: 12494394312n,
  },
];
