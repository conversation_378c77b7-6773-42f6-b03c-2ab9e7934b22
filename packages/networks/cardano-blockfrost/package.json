{"name": "@rosen-chains/cardano-blockfrost-network", "version": "8.0.0", "description": "a package to be used as network api provider for @rosen-chains/cardano package", "repository": {"type": "git", "url": "git+https://github.com/rosen-bridge/rosen-chains.git"}, "license": "MIT", "author": "Rosen Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "CHANGELOG.md"], "scripts": {"build": "tsc --build tsconfig.build.json", "coverage": "npm run test -- --run --coverage", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore", "release": "npm run test -- --run && npm run build && npm publish --access public", "test": "NODE_OPTIONS='--import tsx' vitest", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitest/coverage-istanbul": "^3.1.4", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "prettier": "2.7.1", "tsx": "^4.19.4", "typescript": "^5.0.0", "vitest": "^3.1.4"}, "engines": {"node": ">=20.11.0"}, "dependencies": {"@blockfrost/blockfrost-js": "^6.0.0", "@emurgo/cardano-serialization-lib-nodejs": "^13.2.0", "@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-chains/abstract-chain": "^14.0.0", "@rosen-chains/cardano": "^13.0.0"}}