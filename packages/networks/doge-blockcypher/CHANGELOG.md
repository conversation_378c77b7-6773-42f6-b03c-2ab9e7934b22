# @rosen-chains/doge-blockcypher

## 0.2.0

### Minor Changes

- Add getActualTxId method to chains

### Patch Changes

- Update dependencies
  - @rosen-chains/doge@2.2.0
  - @rosen-chains/abstract-chain@14.0.0

## 0.1.3

### Patch Changes

- Update dependencies
  - @rosen-chains/doge@2.1.1

## 0.1.2

### Patch Changes

- Add error handling to the `getAddressBoxes`
- Improve the `getTxConfirmations` method to avoid extra requests to the explorer when the transaction is finalized.
- Inherit partial doge network
- Fix transaction submission
- Update dependencies
  - @rosen-chains/doge@2.1.0

## 0.1.1

### Patch Changes

- Enhance logging and fix undefined issue for the txrefs field
- Filter out unconfirmed txs
- Update dependencies
  - @rosen-chains/doge@2.0.1
