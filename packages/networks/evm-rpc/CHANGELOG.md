# @rosen-chains/evm-rpc

## 3.1.0

### Minor Changes

- Add getActualTxId method to chains

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@14.0.0
  - @rosen-chains/evm@8.2.0

## 3.0.5

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@13.1.0
  - @rosen-chains/evm@8.1.0

## 3.0.4

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@13.0.0
  - @rosen-chains/evm@8.0.0

## 3.0.3

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@12.0.0
  - @rosen-chains/evm@7.0.0

## 3.0.2

### Patch Changes

- Fix EVM gas estimation so that only required fields are sent to the RPC

## 3.0.1

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.3
  - @rosen-chains/evm@6.0.1

## 3.0.0

### Major Changes

- Replace getMaxFeePerGas and getMaxPriorityFeePer<PERSON>as functions with getFeeData function in AbstractEvmNetwork

### Patch Changes

- Update dependencies
  - @rosen-chains/evm@6.0.0
  - @rosen-chains/abstract-chain@11.0.2

## 2.1.9

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.1
  - @rosen-chains/evm@5.1.1

## 2.1.8

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.0
  - @rosen-chains/evm@5.1.0

## 2.1.7

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@10.0.0
  - @rosen-chains/evm@5.0.0

## 2.1.6

### Patch Changes

- Update dependencies
  - @rosen-chains/evm@4.1.2

## 2.1.5

### Patch Changes

- Update dependencies
  - @rosen-chains/evm@4.1.1

## 2.1.4

### Patch Changes

- Add CALL_EXCEPTION log info to thrown error
- Update dependencies
  - @rosen-chains/evm@4.1.0

## 2.1.3

### Patch Changes

- Update dependencies
  - @rosen-chains/evm@4.0.2
  - @rosen-chains/abstract-chain@9.0.2

## 2.1.2

### Patch Changes

- Improve log in `getGasRequired` function
- Update dependencies
  - @rosen-chains/evm@4.0.1

## 2.1.1

### Patch Changes

- Fix gas estimation
- Update dependencies
  - @rosen-chains/evm@4.0.0

## 2.1.0

### Minor Changes

- Add getTransactionByNonce

### Patch Changes

- Update evm-address-tx-extractor version
- Fix transaction submission
- Update dependencies
  - @rosen-chains/evm@3.0.0
  - @rosen-chains/abstract-chain@9.0.1

## 2.0.1

### Patch Changes

- Update dependencies
  - @rosen-chains/evm@2.0.1

## 2.0.0

### Major Changes

- Consider transaction failure

### Patch Changes

- Update evm-address-tx-extractor
- Update dependencies
  - @rosen-chains/evm@2.0.0
  - @rosen-chains/abstract-chain@9.0.0

## 1.0.0

### Patch Changes

- Update dependencies
  - @rosen-chains/evm@1.0.0
