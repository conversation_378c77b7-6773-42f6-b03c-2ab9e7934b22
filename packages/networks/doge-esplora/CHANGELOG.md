# @rosen-chains/doge-esplora

## 1.1.0

### Minor Changes

- Add getActualTxId method to chains

### Patch Changes

- Update dependencies
  - @rosen-chains/doge@2.2.0
  - @rosen-chains/abstract-chain@14.0.0

## 1.0.3

### Patch Changes

- Update dependencies
  - @rosen-chains/doge@2.1.1

## 1.0.2

### Patch Changes

- Update dependencies
  - @rosen-chains/doge@2.1.0

## 1.0.1

### Patch Changes

- Update dependencies
  - @rosen-chains/doge@2.0.1

## 1.0.0

### Major Changes

- Implement isTxInMempool method and removed getMempoolTxIds

### Minor Changes

- Add optional API prefix for the Esplora network

### Patch Changes

- Update dependencies
- Update dependencies
  - @rosen-chains/abstract-chain@13.1.0
  - @rosen-chains/doge@2.0.0

## 0.2.0

### Minor Changes

- Fix the transaction type in the `getSavedTransactionById` function

## 0.1.1

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@13.0.0
  - @rosen-chains/doge@1.0.0
