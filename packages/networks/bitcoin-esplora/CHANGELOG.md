# @rosen-chains/bitcoin-esplora

## 4.2.0

### Minor Changes

- Add getActualTxId method to chains

### Patch Changes

- Update dependencies
  - @rosen-chains/bitcoin@8.1.0
  - @rosen-chains/abstract-chain@14.0.0

## 4.1.0

### Minor Changes

- Add optional API prefix for the Esplora network

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@13.1.0
  - @rosen-chains/bitcoin@8.0.1

## 4.0.10

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@13.0.0
  - @rosen-chains/bitcoin@8.0.0

## 4.0.9

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@12.0.0
  - @rosen-chains/bitcoin@7.0.0

## 4.0.8

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.3
  - @rosen-chains/bitcoin@6.1.3

## 4.0.7

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.2
  - @rosen-chains/bitcoin@6.1.2

## 4.0.6

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.1
  - @rosen-chains/bitcoin@6.1.1

## 4.0.5

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@11.0.0
  - @rosen-chains/bitcoin@6.1.0

## 4.0.4

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@10.0.0
  - @rosen-chains/bitcoin@6.0.0

## 4.0.3

### Patch Changes

- Update dependencies
  - @rosen-chains/bitcoin@5.1.0

## 4.0.2

### Patch Changes

- Improve logs
- Update dependencies
  - @rosen-chains/bitcoin@5.0.2
  - @rosen-chains/abstract-chain@9.0.2

## 4.0.1

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@9.0.1
  - @rosen-chains/bitcoin@5.0.1

## 4.0.0

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@9.0.0
  - @rosen-chains/bitcoin@5.0.0

## 3.0.0

### Patch Changes

- Update dependencies
  - @rosen-chains/bitcoin@4.0.0
  - @rosen-chains/abstract-chain@8.0.0

## 2.0.1

### Patch Changes

- Update dependencies
  - @rosen-chains/abstract-chain@7.0.1
  - @rosen-chains/bitcoin@3.0.0

## 2.0.0

### Patch Changes

- Fix submit api route
- Update dependencies
  - @rosen-chains/abstract-chain@7.0.0
  - @rosen-chains/bitcoin@2.0.0

## 1.0.0

### Major Changes

- Remove rosen-extractor initialization

### Patch Changes

- Update dependencies
  - @rosen-chains/bitcoin@1.0.0
  - @rosen-chains/abstract-chain@6.0.0
