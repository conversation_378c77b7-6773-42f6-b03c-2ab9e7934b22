{"name": "@rosen-chains/doge-rpc", "version": "0.1.2", "description": "A package to be used as network api provider for @rosen-chains/doge package using RPC API", "repository": {"type": "git", "url": "git+https://github.com/rosen-bridge/rosen-chains.git"}, "license": "MIT", "author": "Rosen Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "CHANGELOG.md"], "scripts": {"build": "tsc --build tsconfig.build.json", "coverage": "npm run test -- --run --coverage", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore", "release": "npm run test -- --run && npm run build && npm publish --access public", "test": "NODE_OPTIONS='--import tsx' vitest", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitest/coverage-istanbul": "^3.1.4", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "tsx": "^4.19.4", "typescript": "^5.3.3", "vitest": "^3.1.4"}, "engines": {"node": ">=20.11.0"}, "dependencies": {"@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-bridge/json-bigint": "^0.1.0", "@rosen-chains/abstract-chain": "^14.0.0", "@rosen-chains/doge": "^2.2.0", "axios": "^1.6.7", "axios-rate-limit": "^1.4.0", "bitcoinjs-lib": "^6.1.5"}}