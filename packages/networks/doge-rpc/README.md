# @rosen-chains/doge-rpc

## Table of contents

- [@rosen-chains/doge-rpc](#rosen-chainsdoge-rpc)
  - [Table of contents](#table-of-contents)
  - [Introduction](#introduction)
  - [Installation](#installation)

## Introduction

A package to be used as network api provider for @rosen-chains/doge package

## Installation

npm:

```sh
npm i @rosen-chains/doge-rpc
```

yarn:

```sh
yarn add @rosen-chains/doge-rpc
```
