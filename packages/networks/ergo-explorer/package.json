{"name": "@rosen-chains/ergo-explorer-network", "version": "9.1.0", "description": "ergo explorer network package for rosen ergo chain", "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "files": ["dist", "CHANGELOG.md"], "type": "module", "scripts": {"prettify": "prettier --write . --ignore-path .gitignore", "lint": "eslint --fix . && npm run prettify", "build": "tsc --build tsconfig.build.json", "type-check": "tsc --noEmit", "test": "NODE_OPTIONS='--import tsx' vitest", "coverage": "npm run test -- --run --coverage", "release": "npm run test -- --run && npm run build && npm publish --access public"}, "repository": {"type": "git", "url": "git+https://github.com/rosen-bridge/rosen-chains.git"}, "license": "MIT", "dependencies": {"@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-bridge/json-bigint": "^0.1.0", "@rosen-chains/abstract-chain": "^14.0.0", "@rosen-chains/ergo": "^12.1.0", "@rosen-clients/ergo-explorer": "^1.1.3", "ergo-lib-wasm-nodejs": "^0.24.1", "it-all": "^3.0.1"}, "devDependencies": {"@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.26.0", "@vitest/coverage-istanbul": "^3.1.4", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.7.1", "tsx": "^4.19.4", "typescript": "^5.0.4", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-wasm": "^3.2.2", "vitest": "^3.1.4"}, "engines": {"node": ">=20.11.0"}, "directories": {"lib": "lib", "test": "tests"}, "keywords": ["rosen"]}